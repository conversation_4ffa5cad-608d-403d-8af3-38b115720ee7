package adhoc.helper

import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import org.springframework.boot.SpringApplication
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
// import org.web3j.protocol.websocket.WebSocketHandshakeException // Not available in current Web3j version
import org.mockito.MockedStatic
import org.mockito.Mockito
import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.*

class AdhocHelper {

	private static localStackPort

	private static DockerComposeContainer composeContainer
	public static final String LOCAL_STACK_PORT = "4566"
	public static final String LOCALSTACK = "localstack"

	static String getLocalStackPort() {
		return localStackPort
	}

	static {
		startContainer()
	}

	private static void startContainer() {
		composeContainer = new DockerComposeContainer(new File("docker-compose-test.yml"))
				.withExposedService(LOCALSTACK, 4566)
				.waitingFor(LOCALSTACK, Wait.forListeningPort())

		composeContainer.start()
		// Get the actual mapped port from the container
		localStackPort = composeContainer.getServicePort(LOCALSTACK, 4566).toString()
		println("LocalStack started successfully on port: " + localStackPort)
	}

	static void cleanupSpec() {
		//To do cleanup Spec
	}

	static boolean tableExists(DynamoDbClient dynamoDbClient, String tableName) {
		println("Checking if table exists: " + tableName)
		DescribeTableRequest request = DescribeTableRequest.builder()
				.tableName(tableName)
				.build()

		try {
			def result = dynamoDbClient.describeTable(request)
			println("Table exists: " + tableName)
			return true
		} catch (ResourceNotFoundException ex) {
			println("Table does not exist: " + tableName)
			return false
		} catch (Exception e) {
			println("Error checking table existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createEventsTable(DynamoDbClient dynamoDbClient, String tableName) {
		if (!tableExists(dynamoDbClient, tableName)) {
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("transactionHash").keyType(KeyType.HASH).build(),
					KeySchemaElement.builder().attributeName("logIndex").keyType(KeyType.RANGE).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("transactionHash").attributeType(ScalarAttributeType.S).build(),
					AttributeDefinition.builder().attributeName("logIndex").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			dynamoDbClient.createTable(createTableRequest)
		}
	}

	static void createBlockHeightTable(DynamoDbClient dynamoDbClient, String tableName) {
		println("Attempting to create table: " + tableName)
		if (!tableExists(dynamoDbClient, tableName)) {
			println("Table does not exist, creating: " + tableName)
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("id").keyType(KeyType.HASH).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("id").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build()

			try {
				def result = dynamoDbClient.createTable(createTableRequest)
				println("Table created successfully: " + tableName)
			} catch (Exception e) {
				println("Error creating table: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Table already exists: " + tableName)
		}
	}

	static boolean bucketExists(S3Client s3Client, String bucketName) {
		println("Checking if bucket exists: " + bucketName)
		try {
			s3Client.headBucket(HeadBucketRequest.builder()
					.bucket(bucketName)
					.build())
			println("Bucket exists: " + bucketName)
			return true
		} catch (NoSuchBucketException ex) {
			println("Bucket does not exist: " + bucketName)
			return false
		} catch (Exception e) {
			println("Error checking bucket existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createS3Bucket(S3Client s3Client, String bucketName) {
		println("Attempting to create bucket: " + bucketName)
		if (!bucketExists(s3Client, bucketName)) {
			println("Bucket does not exist, creating: " + bucketName)
			try {
				s3Client.createBucket(CreateBucketRequest.builder()
						.bucket(bucketName)
						.build())
				println("Bucket created successfully: " + bucketName)
			} catch (Exception e) {
				println("Error creating bucket: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Bucket already exists: " + bucketName)
		}
	}

	static Map<String, AttributeValue> getEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build()

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception e) {
			return null
		}
	}

	static Map<String, AttributeValue> getBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build()

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception e) {
			return null
		}
	}

	static boolean saveEventItem(DynamoDbClient dynamoDbClient, String tableName, Map<String, AttributeValue> item) {
		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build()

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean saveBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id, long blockNumber) {
		Map<String, AttributeValue> item = new HashMap<>()
		item.put("id", AttributeValue.builder().n(String.valueOf(id)).build())
		item.put("blockNumber", AttributeValue.builder().n(String.valueOf(blockNumber)).build())

		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build()

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean deleteEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build()

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static boolean deleteBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build()

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception e) {
			return false
		}
	}

	static ConfigurableApplicationContext initApplication() {
		def application = new SpringApplication(BcmonitoringApplication.class)
		return application.run([] as String[])
	}

	static ConfigurableApplicationContext initApplication(Map<String, Object> defaultProperties) {
		def application = new SpringApplication(BcmonitoringApplication.class)
		if (defaultProperties) {
			application.setDefaultProperties(defaultProperties)
		}
		return application.run([] as String[])
	}

	static void closeApplication(ConfigurableApplicationContext applicationContext) {
		if (applicationContext != null) {
			try {
				println("Closing application context")
				applicationContext.close()
				Thread.sleep(1000) // Give time for port to be released
			} catch (Exception e) {
				println("Error closing application context: " + e.message)
			}
		}
	}

	// ========== WebSocket Testing Utilities ==========

	/**
	 * Create a mock WebSocket service with configurable behavior
	 * Similar to bcclient's WebSocketUtil.generateWebSocketService() mocking
	 */
	static WebSocketService createMockWebSocketService(boolean shouldSucceed = true) {
		WebSocketService mockService = mock(WebSocketService.class)

		if (shouldSucceed) {
			// Mock successful connection
			doNothing().when(mockService).connect()
			when(mockService.isConnected()).thenReturn(true)
			doNothing().when(mockService).close()
		} else {
			// Mock failing connection
			doThrow(new IOException("WebSocket connection failed"))
				.when(mockService).connect()
			when(mockService.isConnected()).thenReturn(false)
			doNothing().when(mockService).close()
		}

		return mockService
	}

	/**
	 * Create a mock Web3j instance with configurable WebSocket behavior
	 */
	static Web3j createMockWeb3j(boolean shouldSucceed = true, boolean simulateHandshakeError = false) {
		Web3j mockWeb3j = mock(Web3j.class)

		if (shouldSucceed && !simulateHandshakeError) {
			// Mock successful blockchain operations
			def blockNumberResponse = new org.web3j.protocol.core.methods.response.EthBlockNumber()
			blockNumberResponse.setResult("0x1234")
			def blockNumberRequest = mock(org.web3j.protocol.core.Request.class)
			when(blockNumberRequest.send()).thenReturn(blockNumberResponse)
			when(mockWeb3j.ethBlockNumber()).thenReturn(blockNumberRequest)

			// Mock successful block flowable
			when(mockWeb3j.blockFlowable(anyBoolean())).thenReturn(io.reactivex.Flowable.empty())

			// Mock successful shutdown
			doNothing().when(mockWeb3j).shutdown()
		} else if (simulateHandshakeError) {
			// Mock WebSocket handshake error
			when(mockWeb3j.blockFlowable(anyBoolean()))
				.thenThrow(new RuntimeException("WebSocket handshake failed"))
			when(mockWeb3j.ethBlockNumber())
				.thenThrow(new RuntimeException("WebSocket handshake failed"))
		} else {
			// Mock general connection failure
			when(mockWeb3j.blockFlowable(anyBoolean()))
				.thenThrow(new IOException("Connection failed"))
			when(mockWeb3j.ethBlockNumber())
				.thenThrow(new IOException("Connection failed"))
		}

		return mockWeb3j
	}

	/**
	 * Setup static mocking for Web3j.build() method
	 * This allows intercepting WebSocket service creation similar to bcclient
	 */
	static MockedStatic<Web3j> setupWeb3jStaticMocking(Web3j mockWeb3jInstance) {
		// Note: Caller should handle cleanup of existing static mocks
		MockedStatic<Web3j> mockedWeb3j = mockStatic(Web3j.class)
		mockedWeb3j.when(() -> Web3j.build(any(WebSocketService.class)))
			.thenReturn(mockWeb3jInstance)
		return mockedWeb3j
	}

	/**
	 * Create WebSocket test configuration properties
	 */
	static Map<String, String> createWebSocketTestProperties(String host = "localhost", String port = "8545") {
		return [
			"bcmonitoring.websocket.uri.host": host,
			"bcmonitoring.websocket.uri.port": port,
			"bcmonitoring.websocket.secure": "false",
			"bcmonitoring.websocket.reconnect.enabled": "true",
			"bcmonitoring.websocket.reconnect.maxAttempts": "3",
			"bcmonitoring.websocket.reconnect.delayMs": "1000"
		]
	}

	/**
	 * Verify WebSocket connection attempts
	 */
	static void verifyWebSocketConnectionAttempts(WebSocketService mockService, int expectedAttempts) {
		verify(mockService, times(expectedAttempts)).connect()
	}

	/**
	 * Verify Web3j operations were called
	 */
	static void verifyWeb3jOperations(Web3j mockWeb3j, boolean shouldHaveBeenCalled = true) {
		if (shouldHaveBeenCalled) {
			verify(mockWeb3j, atLeastOnce()).blockFlowable(anyBoolean())
		} else {
			verify(mockWeb3j, never()).blockFlowable(anyBoolean())
		}
	}
}
