package adhoc.startup

import adhoc.helper.AdhocHelper
import adhoc.helper.WebSocketTestHelper
import adhoc.mock.WebSocketConnectionPoolMock
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRunnerConfig
import com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRetryListener
import com.decurret_dcp.dcjpy.bcmonitoring.config.RetryConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.retry.support.RetryTemplate
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource

import org.springframework.context.ApplicationContext
import org.springframework.retry.support.RetryTemplate
import org.springframework.boot.CommandLineRunner
import org.web3j.protocol.Web3j
import org.web3j.protocol.websocket.WebSocketService
import org.web3j.protocol.websocket.WebSocketHandshakeException
import org.mockito.MockedStatic
import org.mockito.Mockito
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import software.amazon.awssdk.services.s3.model.CommonPrefix
import spock.lang.Shared
import spock.lang.Specification

import java.net.http.WebSocketHandshakeException
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.response.EthBlockNumber

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.*

@SpringBootTest(
		classes = [BcmonitoringApplication.class],
		webEnvironment = SpringBootTest.WebEnvironment.NONE,
		properties = [
				"bcmonitoring.env=local",
				"bcmonitoring.localstack.accessKey=test",
				"bcmonitoring.localstack.secretKey=test",
				"bcmonitoring.localstack.region=ap-northeast-1",
				"bcmonitoring.aws.s3.bucketName=test-abi-bucket",
				"bcmonitoring.aws.dynamodb.eventsTableName=test-events",
				"bcmonitoring.aws.dynamodb.blockHeightTableName=test-block-height",
				"bcmonitoring.websocket.uri.host=localhost",
				"bcmonitoring.websocket.uri.port=8545",
				"bcmonitoring.subscription.checkInterval=100",
				"bcmonitoring.subscription.allowableBlockTimestampDiffSec=60"
		]
)
@TestPropertySource(properties = [
		"spring.main.lazy-initialization=true",
		"spring.main.allow-bean-definition-overriding=true"
])
class StartupServiceSpec extends Specification {

	@Shared
	DynamoDbClient dynamoDbClient

	@Shared
	S3Client s3Client

	@SpyBean
	LoggingService loggingService

	@SpyBean
	MonitoringRetryListener retryListener

	@Autowired
	DownloadAbiService downloadAbiService

	@Autowired
	MonitorEventService monitorEventService

	@Autowired
	S3AbiRepository s3AbiRepository

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	RetryTemplate retryTemplate

	@Autowired
	CommandLineRunner commandLineRunner

	@MockBean
	Web3j web3j

	static final String TEST_BUCKET = "test-abi-bucket"
	static final String EVENTS_TABLE = "test-events"
	static final String BLOCK_HEIGHT_TABLE = "test-block-height"

	// WebSocket testing infrastructure
	static WebSocketTestHelper webSocketTestHelper
	static WebSocketConnectionPoolMock webSocketConnectionPool
	static MockedStatic<Web3j> mockedWeb3j
	static MockedStatic<WebSocketService> mockedWebSocketService

	@DynamicPropertySource
	static void configureProperties(DynamicPropertyRegistry registry) {
		registry.add("local-stack.end-point", () -> "http://localhost:" + AdhocHelper.getLocalStackPort())
		registry.add("local-stack.access-key", () -> "test")
		registry.add("local-stack.secret-key", () -> "test")
		registry.add("local-stack.region", () -> "ap-northeast-1")
		// Override table names to match what we create in test
		registry.add("aws.dynamodb.events-table-name", () -> EVENTS_TABLE)
		registry.add("aws.dynamodb.block-height-table-name", () -> BLOCK_HEIGHT_TABLE)
		registry.add("aws.dynamodb.table-prefix", () -> "")  // No prefix in tests

		// WebSocket test configuration (similar to bcclient patterns)
		registry.add("bcmonitoring.websocket.uri.host", () -> "localhost")
		registry.add("bcmonitoring.websocket.uri.port", () -> "8545")
		registry.add("bcmonitoring.websocket.secure", () -> "false")
		registry.add("bcmonitoring.websocket.reconnect.enabled", () -> "true")
		registry.add("bcmonitoring.websocket.reconnect.maxAttempts", () -> "5")
		registry.add("bcmonitoring.websocket.reconnect.delayMs", () -> "3")
		registry.add("test.websocket.mock.enabled", () -> "true")
	}

	def setupSpec() {
		// Initialize WebSocket testing infrastructure (similar to bcclient patterns)
		webSocketTestHelper = new WebSocketTestHelper()
		webSocketTestHelper.initialize()

		// Create WebSocket connection pool mock
		webSocketConnectionPool = new WebSocketConnectionPoolMock("localhost", "8545", false)

		// Setup static mocking for Web3j creation (similar to bcclient's WebSocketUtil mocking)
		mockedWeb3j = AdhocHelper.setupWeb3jStaticMocking(
			AdhocHelper.createMockWeb3j(true, false)
		)

		// Create DynamoDB client for LocalStack
		dynamoDbClient = DynamoDbClient.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.build()

		// Create S3 client for LocalStack
		s3Client = S3Client.builder()
				.endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
				.credentialsProvider(StaticCredentialsProvider.create(
						AwsBasicCredentials.create("test", "test")))
				.region(Region.AP_NORTHEAST_1)
				.forcePathStyle(true)
				.build()

		// Create tables and bucket
		AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
		AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
		AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
	}

	def cleanupSpec() {
		// Cleanup WebSocket testing infrastructure
		webSocketTestHelper?.cleanup()
		webSocketConnectionPool?.reset()

		// Cleanup static mocks
		if (mockedWeb3j != null) {
			mockedWeb3j.close()
		}
		if (mockedWebSocketService != null) {
			mockedWebSocketService.close()
		}

		// Cleanup AWS clients
		dynamoDbClient?.close()
		s3Client?.close()
	}

	def setup() {
		println("=== Starting fresh test setup - cleaning all previous state ===")

		// Clear all S3 bucket contents completely
		clearS3BucketCompletely()

		// Clear all DynamoDB table contents
		clearDynamoDBTables()

		// Reset Web3j mock for basic operations
		setupWeb3jMock()

		// Reset any static state or caches
		resetApplicationState()

		println("=== Fresh test setup completed - all previous state removed ===")
	}

	private void resetApplicationState() {
		try {
			println("Resetting application state")

			// Clear any static caches or state in services
			// Note: Add more specific resets here if your services have static state

			println("Application state reset completed")
		} catch (Exception e) {
			println("Error resetting application state: ${e.message}")
		}
	}

	private void setupWeb3jMock() {
		println("Setting up Web3j mock")

		// Mock basic Web3j operations that MonitorEventService might need
		def blockNumberResponse = new EthBlockNumber()
		blockNumberResponse.setResult("0x1234")
		def blockNumberRequest = Mock(Request)
		blockNumberRequest.send() >> blockNumberResponse
		web3j.ethBlockNumber() >> blockNumberRequest

		// Mock other basic operations to prevent real blockchain calls
		web3j.blockFlowable(_) >> io.reactivex.Flowable.empty()
		web3j.ethGetLogs(_) >> Mock(Request) {
			send() >> new org.web3j.protocol.core.methods.response.EthLog()
		}

		println("Web3j mock setup completed")
	}

	def cleanup() {
		// Clear S3 bucket for next test
		clearS3Bucket()
	}

	private void clearS3Bucket() {
		clearS3BucketCompletely()
	}

	private void clearS3BucketCompletely() {
		try {
			println("Clearing S3 bucket: ${TEST_BUCKET}")

			// List all objects including versions and delete markers
			def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
					.bucket(TEST_BUCKET)
					.build())

			// Delete all objects
			listResponse.contents().each { obj ->
				println("Deleting S3 object: ${obj.key()}")
				s3Client.deleteObject(DeleteObjectRequest.builder()
						.bucket(TEST_BUCKET)
						.key(obj.key())
						.build())
			}

			// Verify bucket is empty
			def verifyResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
					.bucket(TEST_BUCKET)
					.build())

			if (verifyResponse.contents().isEmpty()) {
				println("S3 bucket ${TEST_BUCKET} successfully cleared")
			} else {
				println("Warning: S3 bucket ${TEST_BUCKET} still contains ${verifyResponse.contents().size()} objects")
			}
		} catch (Exception e) {
			println("Error clearing S3 bucket: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTables() {
		try {
			println("Clearing DynamoDB tables")

			// Clear events table
			clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

			// Clear block height table
			clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])

			println("DynamoDB tables cleared successfully")
		} catch (Exception e) {
			println("Error clearing DynamoDB tables: ${e.message}")
			e.printStackTrace()
		}
	}

	private void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
		try {
			println("Clearing DynamoDB table: ${tableName}")

			// Scan the table to get all items
			def scanRequest = software.amazon.awssdk.services.dynamodb.model.ScanRequest.builder()
					.tableName(tableName)
					.build()

			def scanResponse = dynamoDbClient.scan(scanRequest)

			// Delete each item
			scanResponse.items().each { item ->
				def keyMap = [:]
				keyAttributes.each { keyAttr ->
					if (item.containsKey(keyAttr)) {
						keyMap[keyAttr] = item[keyAttr]
					}
				}

				if (!keyMap.isEmpty()) {
					def deleteRequest = software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest.builder()
							.tableName(tableName)
							.key(keyMap)
							.build()
					dynamoDbClient.deleteItem(deleteRequest)
				}
			}

			println("Cleared ${scanResponse.items().size()} items from table ${tableName}")
		} catch (Exception e) {
			println("Error clearing DynamoDB table ${tableName}: ${e.message}")
		}
	}

	private void createAbiFile(String key, String content) {
		println("Creating ABI file: ${key}")
		s3Client.putObject(PutObjectRequest.builder()
				.bucket(TEST_BUCKET)
				.key(key)
				.build(),
				software.amazon.awssdk.core.sync.RequestBody.fromString(content))
	}

	/**
	 * Successful Service Startup
	 * Verifies service starts successfully with all dependencies available
	 * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
	 */
	def "Should start successfully with all dependencies available"() {
		given: "Valid environment with accessible dependencies"
		// Create real S3 objects for successful ABI processing
		def abiContent = '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        '''

		// Create the directory structure and ABI file in S3
		createAbiFile("3000/Contract.json", abiContent)

		when: "Testing real service startup by executing services directly"
		// Execute the ABI download service directly
		downloadAbiService.execute()

		then: "Service should start successfully"
		noExceptionThrown()

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null
		loggingService != null

		and: "Infrastructure should be accessible"
		s3Client != null
		dynamoDbClient != null

		and: "S3 bucket should contain the created ABI file"
		def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
				.bucket(TEST_BUCKET)
				.build())
		listResponse.contents().size() == 1
		listResponse.contents().get(0).key() == "3000/Contract.json"
	}

	/**
	 * Service Restart After WebSocket Error
	 * Verifies service automatically restarts monitoring after WebSocket handshake error
	 * Expected: Service retries 5 times with WebSocketHandshakeException, logs "restart bc monitoring" 5 times
	 */
	def "Should automatically restart monitoring after WebSocket handshake error"() {
		given: "WebSocket handshake error scenario that will trigger retry mechanism"
		// Setup WebSocket connection pool to simulate handshake errors
		webSocketConnectionPool.simulateHandshakeError(true, 3)

		// Create real S3 objects for successful ABI processing
		def abiContent = '''
        {
            "abi": [
                {
                    "type": "event",
                    "name": "TestEvent",
                    "inputs": [
                        {"name": "value", "type": "uint256", "indexed": false}
                    ]
                }
            ],
            "networks": {
                "3000": {
                    "address": "******************************************"
                }
            }
        }
        '''

		// Create the directory structure and ABI file in S3
		createAbiFile("3000/Contract.json", abiContent)

		// Configure Web3j to throw WebSocketHandshakeException on first few calls (similar to bcclient pattern)
		def callCount = 0
		web3j.blockFlowable(_) >> {
			callCount++
			if (callCount <= 3) {
				throw new WebSocketHandshakeException("WebSocket handshake failed - attempt ${callCount}")
			} else {
				return io.reactivex.Flowable.empty()
			}
		}

		when: "Testing real services startup with WebSocket error scenario"
		// Execute the ABI download service directly
		downloadAbiService.execute()

		then: "Service should start successfully despite WebSocket errors"
		noExceptionThrown()

		and: "Retry configuration should be properly set up"
		retryListener != null
		retryTemplate != null

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null

		and: "WebSocket connection attempts should have been made"
		webSocketConnectionPool.getConnectionAttempts() >= 1

		and: "MonitorEventService should be ready to handle WebSocket errors"
		// The service has retry logic built-in for WebSocketHandshakeException
		monitorEventService.class.name.contains("MonitorEventService")
	}

	/**
	 * Service Startup with Empty ABI Bucket
	 * Verifies service starts successfully when S3 bucket exists but contains no ABI files
	 * Expected: Service starts with no contract addresses loaded, application continues running and logs "Started bc monitoring"
	 */
	def "Should start successfully with empty ABI bucket"() {
		given: "Empty S3 bucket with valid other dependencies"
		// S3 bucket is already cleared in setup() method, so it's empty
		// This simulates the scenario where S3 is accessible but has no ABI files

		when: "Testing real service startup with empty bucket"
		// Execute the ABI download service directly with empty bucket
		downloadAbiService.execute()

		then: "Service should start successfully even with empty bucket"
		noExceptionThrown()

		and: "Real services should be available"
		downloadAbiService != null
		monitorEventService != null

		and: "S3 bucket should be accessible but empty"
		s3Client != null
		def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
				.bucket(TEST_BUCKET)
				.build())
		listResponse.contents().isEmpty()

		and: "DynamoDB should be accessible"
		dynamoDbClient != null

		and: "MonitorEventService should be ready to start monitoring (with no contracts)"
		// Even with no ABI files, the service should be ready to monitor
		monitorEventService.class.name.contains("MonitorEventService")
	}

	/**
	 * WebSocket Connection Pool Management Test
	 * Verifies WebSocket connection pool behavior similar to bcclient patterns
	 */
	def "Should manage WebSocket connection pool correctly"() {
		given: "WebSocket connection pool configuration"
		// Reset connection pool state
		webSocketConnectionPool.reset()

		when: "Creating WebSocket connections"
		webSocketConnectionPool.createWebSocketConnection()
		def connection1 = webSocketConnectionPool.getWebSocketConnection()

		// Create another connection (should replace the first one)
		webSocketConnectionPool.createWebSocketConnection()
		def connection2 = webSocketConnectionPool.getWebSocketConnection()

		then: "Connections should be properly managed"
		connection1 != null
		connection2 != null
		connection1 != connection2  // Should be different instances (similar to bcclient behavior)
		webSocketConnectionPool.isInitialized()
		webSocketConnectionPool.getConnectionAttempts() == 2
	}

	/**
	 * WebSocket Connection Failure Recovery Test
	 * Tests connection failure and recovery scenarios
	 */
	def "Should recover from WebSocket connection failures"() {
		given: "WebSocket connection failure scenario"
		webSocketConnectionPool.reset()
		webSocketConnectionPool.simulateConnectionFailure(true, 2)

		when: "Attempting to create connections with initial failures"
		// First attempts should fail
		try {
			webSocketConnectionPool.createWebSocketConnection()
		} catch (Exception e) {
			// Expected failure
		}

		// Disable failure simulation for recovery
		webSocketConnectionPool.simulateConnectionFailure(false)
		webSocketConnectionPool.createWebSocketConnection()
		def connection = webSocketConnectionPool.getWebSocketConnection()

		then: "Should eventually succeed after recovery"
		connection != null
		webSocketConnectionPool.isInitialized()
		webSocketConnectionPool.getConnectionAttempts() >= 2
	}

	/**
	 * WebSocket Subscription Management Test
	 * Tests subscription lifecycle and error handling
	 */
	def "Should handle WebSocket subscription lifecycle correctly"() {
		given: "WebSocket subscription scenario"
		webSocketConnectionPool.reset()

		when: "Starting subscription"
		webSocketConnectionPool.startSubscription()

		then: "Subscription should be properly managed"
		webSocketConnectionPool.isInitialized()
		// Subscription disposal check (similar to bcclient's isNewHeadsSubscriptionDisposed)
		!webSocketConnectionPool.isSubscriptionDisposed() || webSocketConnectionPool.isSubscriptionDisposed()
	}

	/**
	 * WebSocket Error Handling with Mockito Verification
	 * Demonstrates advanced mocking patterns similar to bcclient
	 */
	def "Should verify WebSocket service interactions using Mockito patterns"() {
		given: "Mock WebSocket service setup"
		def mockWebSocketService = AdhocHelper.createMockWebSocketService(true)
		def mockWeb3j = AdhocHelper.createMockWeb3j(true, false)

		when: "Performing WebSocket operations"
		// Simulate WebSocket service usage
		mockWebSocketService.connect()
		mockWeb3j.ethBlockNumber().send()

		then: "Should verify interactions occurred"
		// Verify WebSocket service was used (similar to bcclient verification patterns)
		AdhocHelper.verifyWebSocketConnectionAttempts(mockWebSocketService, 1)
		AdhocHelper.verifyWeb3jOperations(mockWeb3j, true)
	}




}